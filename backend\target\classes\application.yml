server:
  port: ${PORT:8080}
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Application Configuration
app:
  name: EcommerceHub
  version: 1.0.0
  description: Modern E-commerce Platform with React and Spring Boot
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}

spring:
  application:
    name: ${app.name}
  
  profiles:
    active: dev
  
  # Database Configuration - PostgreSQL
  datasource:
    url: ${DATABASE_URL:*********************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  # MongoDB Configuration
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/ecommerce_products}
      auto-index-creation: true

    # Redis Configuration (Optional - for caching)
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Mail Configuration
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

  # Cache Configuration
  cache:
    type: simple
    cache-names: products,categories,users

  # Jackson Configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: UTC
    default-property-inclusion: non-null

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# Payment Gateway Configuration
payment:
  stripe:
    public-key: ${STRIPE_PUBLIC_KEY:pk_test_demo}
    secret-key: ${STRIPE_SECRET_KEY:sk_test_demo}
    webhook-secret: ${STRIPE_WEBHOOK_SECRET:whsec_demo}
  razorpay:
    key-id: ${RAZORPAY_KEY_ID:rzp_test_demo}
    key-secret: ${RAZORPAY_KEY_SECRET:demo_secret}

# File Storage Configuration
file:
  upload:
    dir: ${FILE_UPLOAD_DIR:./uploads}
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,webp,pdf

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Logging Configuration
logging:
  level:
    com.ecommerce: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ecommerce.log

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# CORS Configuration
cors:
  allowed-origins: 
    - http://localhost:3000
    - http://localhost:5173
    - https://your-frontend-domain.com
  allowed-methods: GET,POST,PUT,DELETE,PATCH,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

logging:
  level:
    root: INFO
    com.ecommerce: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.ecommerce: INFO
